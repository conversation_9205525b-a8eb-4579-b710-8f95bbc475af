"""
报表统计API
"""
from datetime import date
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.auth.dependencies import get_current_user
from app.utils.export_utils import ExportUtils

router = APIRouter()


@router.get("/monthly/{year}/{month}", summary="获取月度报告")
async def get_monthly_report(
    year: int,
    month: int,
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取指定年月的费用分摊报告（实时计算）

    - **year**: 年份
    - **month**: 月份 (1-12)
    - **end_date**: 截止日期，不指定则统计整月
    """
    if month < 1 or month > 12:
        raise HTTPException(status_code=400, detail="月份必须在1-12之间")

    if year < 1900 or year > 3000:
        raise HTTPException(status_code=400, detail="年份必须在1900-3000之间")

    try:
        # 使用 ReportService 进行正确的月度报告计算
        from app.services.report_service import ReportService

        service = ReportService(db)
        report = await service.get_monthly_report(year, month, end_date)

        # 转换为API响应格式
        dormitory_allocations = []
        for detail in report.daily_details:
            # 按宿舍分组统计
            dorm_found = False
            for dorm_alloc in dormitory_allocations:
                if dorm_alloc["dormitory_name"] == detail.dormitory_name:
                    # 累加该宿舍的数据
                    dorm_alloc["total_bed_days"] += detail.total_beds
                    for dept_alloc in detail.department_allocations:
                        dept_id = dept_alloc["department_id"]
                        if dept_id not in dorm_alloc["departments"]:
                            dorm_alloc["departments"][dept_id] = {
                                "department_name": dept_alloc["department_name"],
                                "bed_days": 0
                            }
                        dorm_alloc["departments"][dept_id]["bed_days"] += detail.total_beds * dept_alloc["allocation_ratio"]
                    dorm_found = True
                    break

            if not dorm_found:
                # 新宿舍
                departments = {}
                for dept_alloc in detail.department_allocations:
                    dept_id = dept_alloc["department_id"]
                    departments[dept_id] = {
                        "department_name": dept_alloc["department_name"],
                        "bed_days": detail.total_beds * dept_alloc["allocation_ratio"]
                    }

                dormitory_allocations.append({
                    "dormitory_name": detail.dormitory_name,
                    "total_bed_days": detail.total_beds,
                    "allocation_percentage": 0.0,  # 稍后计算
                    "departments": departments
                })

        # 计算总床位天数和分摊百分比
        total_bed_days = sum(dorm["total_bed_days"] for dorm in dormitory_allocations)
        for dorm_alloc in dormitory_allocations:
            if total_bed_days > 0:
                dorm_alloc["allocation_percentage"] = round((dorm_alloc["total_bed_days"] / total_bed_days) * 100, 1)

            # 计算宿舍内各部门占比
            dorm_total_days = dorm_alloc["total_bed_days"]
            for dept_info in dorm_alloc["departments"].values():
                if dorm_total_days > 0:
                    dept_info["percentage_in_dorm"] = round((dept_info["bed_days"] / dorm_total_days) * 100, 1)

        # 计算平均入住率
        from app.repositories.dormitory_repo import DormitoryRepository
        dorm_repo = DormitoryRepository(db)
        total_beds = sum(d.total_beds for d in dorm_repo.get_active_dormitories())

        # 计算统计天数
        from datetime import datetime, timedelta
        import calendar
        start_date = date(year, month, 1)
        month_last_day = date(year, month, calendar.monthrange(year, month)[1])
        today = date.today()

        if end_date:
            actual_end_date = min(end_date, month_last_day)
        else:
            if year == today.year and month == today.month and today.day > 1:
                actual_end_date = date(year, month, today.day - 1)
            else:
                actual_end_date = month_last_day

        days_in_period = (actual_end_date - start_date).days + 1
        max_possible_bed_days = total_beds * days_in_period
        average_occupancy_rate = (report.total_bed_days / max_possible_bed_days * 100) if max_possible_bed_days > 0 else 0

        return {
            "average_occupancy_rate": round(average_occupancy_rate, 1),
            "department_count": len(report.department_summary),
            "dormitory_count": len(dormitory_allocations),
            "dormitory_allocations": dormitory_allocations,
            "year": year,
            "month": month,
            "start_date": start_date.isoformat(),
            "end_date": actual_end_date.isoformat(),
            "is_current_month": year == date.today().year and month == date.today().month
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取月度报告失败: {str(e)}")


# 日度分摊明细功能暂时移除，可以后续根据需要添加


@router.get("/realtime", summary="获取实时报告")
async def get_realtime_report(
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """获取当前系统实时统计数据"""
    try:
        # 简化的实时报告，直接查询数据库统计
        from app.repositories.department_repo import DepartmentRepository
        from app.repositories.dormitory_repo import DormitoryRepository
        from app.repositories.resident_repo import ResidentRepository
        from app.repositories.record_repo import RecordRepository

        dept_repo = DepartmentRepository(db)
        dorm_repo = DormitoryRepository(db)
        resident_repo = ResidentRepository(db)
        record_repo = RecordRepository(db)

        # 获取基础统计数据
        departments = dept_repo.get_all()
        dormitories = dorm_repo.get_all()
        residents = resident_repo.get_all()
        active_records = record_repo.get_active_records()

        # 计算统计数据
        total_departments = len([d for d in departments if d.is_active])
        total_dormitories = len([d for d in dormitories if d.is_active])
        total_beds = sum(d.total_beds for d in dormitories if d.is_active)
        occupied_beds = len(active_records)
        total_residents = len([r for r in residents if r.is_active])

        return {
            "total_departments": total_departments,
            "total_dormitories": total_dormitories,
            "total_beds": total_beds,
            "occupied_beds": occupied_beds,
            "available_beds": total_beds - occupied_beds,
            "total_residents": total_residents,
            "occupancy_rate": round((occupied_beds / total_beds * 100) if total_beds > 0 else 0, 1),
            "active_records": len(active_records)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时报告失败: {str(e)}")


@router.get("/monthly/{year}/{month}/export", summary="导出月度报告")
async def export_monthly_report(
    year: int,
    month: int,
    format: str = Query("excel", regex="^(excel|pdf|csv)$", description="导出格式"),
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    导出指定年月的费用分摊报告

    - **year**: 年份
    - **month**: 月份 (1-12)
    - **format**: 导出格式 (excel, pdf, csv)
    - **end_date**: 截止日期，不指定则统计整月
    """
    if month < 1 or month > 12:
        raise HTTPException(status_code=400, detail="月份必须在1-12之间")

    if year < 1900 or year > 3000:
        raise HTTPException(status_code=400, detail="年份必须在1900-3000之间")

    try:
        # 使用 ReportService 获取月度报告数据
        from app.services.report_service import ReportService

        service = ReportService(db)
        report = await service.get_monthly_report(year, month, end_date)

        # 转换报告数据为导出格式（复用月度报告API的逻辑）
        dormitory_allocations = []
        for detail in report.daily_details:
            # 按宿舍分组统计
            dorm_found = False
            for dorm_alloc in dormitory_allocations:
                if dorm_alloc["dormitory_name"] == detail.dormitory_name:
                    # 累加该宿舍的数据
                    dorm_alloc["total_bed_days"] += detail.total_beds
                    for dept_alloc in detail.department_allocations:
                        dept_id = dept_alloc["department_id"]
                        if dept_id not in dorm_alloc["departments"]:
                            dorm_alloc["departments"][dept_id] = {
                                "department_name": dept_alloc["department_name"],
                                "bed_days": 0
                            }
                        dorm_alloc["departments"][dept_id]["bed_days"] += detail.total_beds * dept_alloc["allocation_ratio"]
                    dorm_found = True
                    break

            if not dorm_found:
                # 新宿舍
                departments = {}
                for dept_alloc in detail.department_allocations:
                    dept_id = dept_alloc["department_id"]
                    departments[dept_id] = {
                        "department_name": dept_alloc["department_name"],
                        "bed_days": detail.total_beds * dept_alloc["allocation_ratio"]
                    }

                dormitory_allocations.append({
                    "dormitory_name": detail.dormitory_name,
                    "total_bed_days": detail.total_beds,
                    "allocation_percentage": 0.0,  # 稍后计算
                    "departments": departments
                })

        # 计算总床位天数和分摊百分比
        total_bed_days = sum(dorm["total_bed_days"] for dorm in dormitory_allocations)
        for dorm_alloc in dormitory_allocations:
            if total_bed_days > 0:
                dorm_alloc["allocation_percentage"] = round((dorm_alloc["total_bed_days"] / total_bed_days) * 100, 1)

            # 计算宿舍内各部门占比
            dorm_total_days = dorm_alloc["total_bed_days"]
            for dept_info in dorm_alloc["departments"].values():
                if dorm_total_days > 0:
                    dept_info["percentage_in_dorm"] = round((dept_info["bed_days"] / dorm_total_days) * 100, 1)

        # 计算平均入住率
        from app.repositories.dormitory_repo import DormitoryRepository
        from datetime import datetime, timedelta
        import calendar

        dorm_repo = DormitoryRepository(db)
        total_beds = sum(d.total_beds for d in dorm_repo.get_active_dormitories())

        start_date = date(year, month, 1)
        month_last_day = date(year, month, calendar.monthrange(year, month)[1])
        today = date.today()

        if end_date:
            actual_end_date = min(end_date, month_last_day)
        else:
            if year == today.year and month == today.month and today.day > 1:
                actual_end_date = date(year, month, today.day - 1)
            else:
                actual_end_date = month_last_day

        days_in_period = (actual_end_date - start_date).days + 1
        max_possible_bed_days = total_beds * days_in_period
        average_occupancy_rate = (report.total_bed_days / max_possible_bed_days * 100) if max_possible_bed_days > 0 else 0

        # 准备导出数据
        report_data = {
            "average_occupancy_rate": round(average_occupancy_rate, 1),
            "department_count": len(report.department_summary),
            "dormitory_count": len(dormitory_allocations),
            "dormitory_allocations": dormitory_allocations,
            "year": year,
            "month": month,
            "start_date": start_date.isoformat(),
            "end_date": actual_end_date.isoformat(),
            "is_current_month": year == date.today().year and month == date.today().month
        }

        # 使用导出工具导出文件
        export_utils = ExportUtils()

        if format == "excel":
            file_content, filename, media_type = await export_utils.export_to_excel(report_data)
        elif format == "pdf":
            file_content, filename, media_type = await export_utils.export_to_pdf(report_data)
        elif format == "csv":
            file_content, filename, media_type = await export_utils.export_to_csv(report_data)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的导出格式: {format}")

        # 对文件名进行URL编码以支持中文
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))

        return Response(
            content=file_content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出月度报告失败: {str(e)}")
